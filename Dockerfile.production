# Production Dockerfile for Swinx Laravel Application
# Based on working development configuration
FROM php:8.4-fpm-alpine AS production

# Set environment variables for production
ENV APP_ENV=production
ENV APP_DEBUG=false
ENV COMPOSER_ALLOW_SUPERUSER=1

# Install system dependencies including Node.js (same as dev)
RUN apk add --no-cache \
    nginx \
    zip \
    unzip \
    curl \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    libzip-dev \
    icu-dev \
    oniguruma-dev \
    mysql-client \
    nodejs \
    npm \
    supervisor \
    dcron \
    && rm -rf /var/cache/apk/*

# Install PHP extensions (same as dev)
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mysqli \
        zip \
        gd \
        intl \
        mbstring \
        opcache \
        bcmath \
    && docker-php-ext-enable opcache

# Configure OPcache for production
RUN echo "opcache.enable=1" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.enable_cli=0" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.memory_consumption=256" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.interned_strings_buffer=16" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.max_accelerated_files=20000" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.revalidate_freq=0" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.validate_timestamps=0" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.save_comments=1" >> /usr/local/etc/php/conf.d/opcache.ini \
    && echo "opcache.fast_shutdown=1" >> /usr/local/etc/php/conf.d/opcache.ini

# Install Composer (same as dev)
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create app directory
WORKDIR /var/www/html

# Copy application code first (same as dev)
COPY . .

# Install PHP dependencies (production only - no dev dependencies)
RUN composer install --no-dev --optimize-autoloader --no-interaction --prefer-dist

# Verify debugbar is not installed in production
RUN if [ -d "vendor/barryvdh/laravel-debugbar" ]; then \
        echo "ERROR: Debugbar found in production build!" && exit 1; \
    else \
        echo "✅ Debugbar correctly excluded from production"; \
    fi

# Clear any cached service providers and rebuild autoloader
RUN composer dump-autoload --optimize --classmap-authoritative

# Clear Laravel cached configs that might reference debugbar
RUN rm -rf bootstrap/cache/*.php || true

# Create a clean environment file for production
RUN echo "APP_ENV=production" > .env.production.clean

# Install Node.js dependencies and build frontend assets (same as dev)
RUN npm ci --only=production

# Build frontend assets
RUN npm run build

# Set permissions (same as dev but using www-data)
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache

# Copy configuration files (use existing dev configs as base)
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/http.d/default.conf
COPY docker/php/php.ini /usr/local/etc/php/conf.d/custom.ini
COPY docker/php/php-fpm.conf /usr/local/etc/php-fpm.d/www.conf

# Create startup script (use modified version of dev start.sh)
COPY docker/start-production.sh /start.sh
RUN chmod +x /start.sh

# Expose port
EXPOSE 8080

# Health check (use /up like dev, not /health)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/up || exit 1

# Start application (same as dev)
CMD ["/start.sh"]
