# ===========================================
# Docker Development Environment Configuration
# ===========================================
# For local development with docker-compose

# Application
APP_NAME="Swinx Application (Development)"
APP_ENV=local
APP_KEY=base64:9NX5UrRAaSObXu3roUfs7g8SF70SNt1TQfdck24f8Mk=
APP_DEBUG=true
APP_TIMEZONE=Asia/Ho_Chi_Minh
APP_URL=http://localhost:8080

# Database Configuration (Docker Development)
DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=swinburne
DB_USERNAME=swinx_user
DB_PASSWORD=swinx_password

# Test Database Configuration
DB_TEST_CONNECTION=mysql
DB_TEST_HOST=db-test
DB_TEST_PORT=3306
DB_TEST_DATABASE=swinburne_test
DB_TEST_USERNAME=swinx_user
DB_TEST_PASSWORD=swinx_password

# Redis Configuration (Docker Development)
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Cache & Session (Development)
CACHE_DRIVER=file
CACHE_PREFIX=swinx_cache_dev
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Queue (Development)
QUEUE_CONNECTION=sync
QUEUE_FAILED_DRIVER=database-uuids

# Logging (Development)
LOG_CHANNEL=stderr
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Broadcasting (Development)
BROADCAST_CONNECTION=log
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Mail (Development)
MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Filesystem (Development)
FILESYSTEM_DISK=local

# Development Tools
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=true
VITE_DEV_SERVER_KEY=
VITE_DEV_SERVER_CERT=

# Development Security (Less Strict)
SANCTUM_STATEFUL_DOMAINS=localhost,localhost:8080,127.0.0.1,127.0.0.1:8080
SESSION_SECURE_COOKIE=false

# FrankenPHP Configuration (Development)
FRANKENPHP_NUM_THREADS=auto
SERVER_NAME=localhost


GOOGLE_APPLICATION_CREDENTIALS=storage/xenon-point-407303-ecde80c848d2.json
GOOGLE_DRIVE_FOLDER_ID_EVENT=1cL3ZsWWzVYKMnkoE_52SnPdChIE-IFQY
GOOGLE_DRIVE_FOLDER_ID_QUERY=1EPmGKESv9vpQ_159p4etAoLJN-2wpaYB
GOOGLE_DRIVE_FOLDER_ID_CLUB=1mAzGPeJiph-rep3mHGJSw58IjAW0NR86
GOOGLE_DRIVE_FOLDER_ID_GUIDLINE=10x3esoVT6-SmsN8NVGbwga4Y-TVlb2Oj
GOOGLE_DRIVE_FOLDER_ID_QRCODE=1X64y9uMUfKMBeKopQSlJGiFxxX9gcUZM
GOOGLE_REDIRECT_URI=http://127.0.0.1:8080/auth/google/callback