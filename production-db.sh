#!/bin/bash

# Swinx Production Database Management Script
# Usage: ./production-db.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="swinx-app"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Swinx Database Management${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if container is running
check_container() {
    if ! docker ps | grep -q $CONTAINER_NAME; then
        print_error "Container $CONTAINER_NAME is not running. Please start the application first."
        exit 1
    fi
}

# Run migrations
migrate() {
    print_header
    print_info "Running database migrations..."
    
    check_container
    docker exec $CONTAINER_NAME php artisan migrate --force
    
    print_success "Migrations completed!"
}

# Refresh migrations (WARNING: This will delete all data!)
migrate_refresh() {
    print_header
    print_warning "This will DELETE ALL DATA and refresh the database!"
    read -p "Are you sure? Type 'yes' to continue: " -r
    echo
    
    if [[ $REPLY == "yes" ]]; then
        check_container
        print_info "Refreshing database migrations..."
        docker exec $CONTAINER_NAME php artisan migrate:refresh --force
        print_success "Database refreshed!"
    else
        print_info "Operation cancelled."
    fi
}

# Run seeders
seed() {
    print_header
    print_info "Running database seeders..."
    
    check_container
    docker exec $CONTAINER_NAME php artisan db:seed --force
    
    print_success "Seeders completed!"
}

# Fresh migration with seeding
fresh() {
    print_header
    print_warning "This will DELETE ALL DATA and recreate the database with fresh data!"
    read -p "Are you sure? Type 'yes' to continue: " -r
    echo
    
    if [[ $REPLY == "yes" ]]; then
        check_container
        print_info "Running fresh migrations with seeding..."
        docker exec $CONTAINER_NAME php artisan migrate:fresh --seed --force
        print_success "Fresh database with seeding completed!"
    else
        print_info "Operation cancelled."
    fi
}

# Show migration status
status() {
    print_header
    print_info "Database migration status:"
    
    check_container
    docker exec $CONTAINER_NAME php artisan migrate:status
}

# Rollback migrations
rollback() {
    print_header
    print_warning "This will rollback the last batch of migrations!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        check_container
        print_info "Rolling back migrations..."
        docker exec $CONTAINER_NAME php artisan migrate:rollback --force
        print_success "Rollback completed!"
    else
        print_info "Rollback cancelled."
    fi
}

# Create backup
backup() {
    print_header
    print_info "Creating database backup..."
    
    check_container
    
    # Create backup directory if it doesn't exist
    mkdir -p backups
    
    # Generate backup filename with timestamp
    BACKUP_FILE="backups/swinx_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Create backup
    docker exec swinx-db mysqldump -u root -p"${MYSQL_ROOT_PASSWORD:-secret}" swinx > "$BACKUP_FILE"
    
    print_success "Backup created: $BACKUP_FILE"
}

# Restore backup
restore() {
    print_header
    
    if [ -z "$2" ]; then
        print_error "Please specify backup file to restore."
        echo "Usage: $0 restore <backup_file>"
        echo "Available backups:"
        ls -la backups/*.sql 2>/dev/null || echo "No backups found."
        exit 1
    fi
    
    BACKUP_FILE="$2"
    
    if [ ! -f "$BACKUP_FILE" ]; then
        print_error "Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    print_warning "This will REPLACE ALL DATA with the backup!"
    read -p "Are you sure? Type 'yes' to continue: " -r
    echo
    
    if [[ $REPLY == "yes" ]]; then
        check_container
        print_info "Restoring database from backup..."
        docker exec -i swinx-db mysql -u root -p"${MYSQL_ROOT_PASSWORD:-secret}" swinx < "$BACKUP_FILE"
        print_success "Database restored from: $BACKUP_FILE"
    else
        print_info "Restore cancelled."
    fi
}

# Access database console
console() {
    print_header
    print_info "Opening database console..."
    print_info "Use 'exit' to close the console."
    
    check_container
    docker exec -it swinx-db mysql -u root -p"${MYSQL_ROOT_PASSWORD:-secret}" swinx
}

# Show help
help() {
    print_header
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  migrate         - Run pending migrations"
    echo "  migrate:refresh - Refresh all migrations (DELETES DATA!)"
    echo "  seed           - Run database seeders"
    echo "  fresh          - Fresh migrations with seeding (DELETES DATA!)"
    echo "  status         - Show migration status"
    echo "  rollback       - Rollback last migration batch"
    echo "  backup         - Create database backup"
    echo "  restore <file> - Restore database from backup"
    echo "  console        - Access database console"
    echo "  help           - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 migrate"
    echo "  $0 seed"
    echo "  $0 backup"
    echo "  $0 restore backups/swinx_backup_20250609_185000.sql"
    echo ""
    echo "⚠️  Commands marked with (DELETES DATA!) will remove all existing data!"
}

# Main script logic
case "${1:-help}" in
    migrate)
        migrate
        ;;
    migrate:refresh)
        migrate_refresh
        ;;
    seed)
        seed
        ;;
    fresh)
        fresh
        ;;
    status)
        status
        ;;
    rollback)
        rollback
        ;;
    backup)
        backup
        ;;
    restore)
        restore "$@"
        ;;
    console)
        console
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
