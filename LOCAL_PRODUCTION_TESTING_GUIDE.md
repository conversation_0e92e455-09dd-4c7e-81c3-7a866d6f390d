# 🧟 Local Production Testing Guide

Complete step-by-step guide for testing the FrankenPHP production configuration locally using `swinx.test`.

## 📋 Prerequisites

1. **Hosts File Configuration**: You mentioned you've already configured this ✅
   ```bash
   # Verify your hosts file contains:
   127.0.0.1    swinx.test
   ```

2. **Docker & Docker Compose**: Ensure you have the latest versions
3. **Stop Development Environment**: If running
   ```bash
   docker-compose down
   ```

## 🚀 Step-by-Step Setup

### Step 1: Verify Environment Configuration

The production environment file has been updated for local testing:

```bash
# Check the updated configuration
cat .env.docker.production | grep -E "(APP_URL|SERVER_NAME|SANCTUM|GOOGLE_REDIRECT)"
```

**Expected output:**
```
APP_URL=https://swinx.test
SERVER_NAME=swinx.test
SANCTUM_STATEFUL_DOMAINS=swinx.test,www.swinx.test
GOOGLE_REDIRECT_URI=https://swinx.test/auth/google/callback
```

### Step 2: Build the Production Images

```bash
# Build the production Docker image
docker-compose -f docker-compose.local-prod.yml build --no-cache

# This will:
# - Use Dockerfile.production
# - Install production dependencies
# - Configure FrankenPHP for production
# - Set up SSL certificates
```

### Step 3: Start the Local Production Environment

```bash
# Start all services in production mode
docker-compose -f docker-compose.local-prod.yml up -d

# Check if all containers are running
docker-compose -f docker-compose.local-prod.yml ps
```

**Expected containers:**
- `swinx-app-local-prod` (FrankenPHP application)
- `swinx-db-local-prod` (MySQL database)
- `swinx-redis-local-prod` (Redis cache)

### Step 4: Monitor the Startup Process

```bash
# Watch the application startup logs
docker logs -f swinx-app-local-prod

# Look for these success messages:
# ✅ Database connection established!
# ⚡ Caching configurations for production...
# 🌐 Starting FrankenPHP on ports 80 and 443...
```

### Step 5: Wait for SSL Certificate Generation

FrankenPHP will automatically generate self-signed certificates for `swinx.test`. This may take 30-60 seconds.

```bash
# Check certificate status
docker exec swinx-app-local-prod frankenphp list-certificates

# You should see certificates for swinx.test
```

### Step 6: Test HTTP to HTTPS Redirect

```bash
# Test HTTP redirect (should redirect to HTTPS)
curl -v http://swinx.test/health

# Expected: 301/302 redirect to https://swinx.test/health
```

### Step 7: Test HTTPS Connectivity

```bash
# Test HTTPS with self-signed certificate (ignore SSL warnings)
curl -k -v https://swinx.test/health

# Expected: 200 OK with "healthy" response
```

### Step 8: Test Laravel Application

```bash
# Test Laravel health endpoint
curl -k https://swinx.test/up

# Expected: "up" response
```

### Step 9: Run Comprehensive Tests

```bash
# Run the automated test suite
./scripts/test-local-production.sh

# This will test:
# - HTTP to HTTPS redirect
# - SSL certificate validity
# - Security headers (HSTS, CSP, etc.)
# - Compression (gzip/brotli)
# - Static file serving and caching
# - Rate limiting
# - Database and Redis connectivity
# - Performance metrics
```

## 🔍 Verification Checklist

### ✅ Basic Functionality
- [ ] Application loads at https://swinx.test
- [ ] HTTP redirects to HTTPS
- [ ] SSL certificate is valid for swinx.test
- [ ] Laravel application responds correctly

### ✅ Security Features
- [ ] HSTS header present
- [ ] Security headers (X-Frame-Options, CSP, etc.)
- [ ] Rate limiting on sensitive endpoints
- [ ] Secure cookies enabled
- [ ] Server information hidden

### ✅ Performance Features
- [ ] Gzip/Brotli compression enabled
- [ ] Static file caching headers
- [ ] Fast response times (<1000ms)
- [ ] HTTP/2 support

### ✅ Production Configuration
- [ ] Production environment variables
- [ ] Optimized PHP settings (OPcache)
- [ ] Redis caching enabled
- [ ] Production logging configured

## 🌐 Browser Testing

### Access the Application
1. Open your browser and navigate to: **https://swinx.test**
2. **Accept the self-signed certificate warning** (this is expected for local testing)
3. Verify the application loads correctly

### Test HTTP Redirect
1. Navigate to: **http://swinx.test**
2. Verify it automatically redirects to **https://swinx.test**

### Check Security Headers
1. Open browser developer tools (F12)
2. Go to Network tab
3. Refresh the page
4. Check response headers for:
   - `Strict-Transport-Security`
   - `X-Frame-Options`
   - `Content-Security-Policy`
   - `X-Environment: local-production`

## 🔧 Troubleshooting

### Common Issues

#### 1. Certificate Errors
```bash
# If SSL certificate issues occur
docker exec swinx-app-local-prod frankenphp reload

# Check Caddy logs
docker logs swinx-app-local-prod | grep -i certificate
```

#### 2. Port Conflicts
```bash
# Check if ports 80/443 are in use
sudo lsof -i :80
sudo lsof -i :443

# Stop conflicting services
sudo systemctl stop nginx  # if running
sudo systemctl stop apache2  # if running
```

#### 3. Database Connection Issues
```bash
# Check database container
docker-compose -f docker-compose.local-prod.yml logs db

# Test database connection
docker exec swinx-app-local-prod php artisan tinker
>>> DB::connection()->getPdo();
```

#### 4. Redis Connection Issues
```bash
# Check Redis container
docker-compose -f docker-compose.local-prod.yml logs redis

# Test Redis connection
docker exec swinx-app-local-prod php artisan tinker
>>> Redis::ping();
```

## 📊 Performance Testing

### Load Testing
```bash
# Install Apache Bench (if not installed)
# macOS: brew install httpd
# Ubuntu: sudo apt install apache2-utils

# Run load test
ab -n 100 -c 10 -k https://swinx.test/

# Test with self-signed certificate
ab -n 100 -c 10 -k -Z TLS1.2 https://swinx.test/
```

### Response Time Testing
```bash
# Test response times
for i in {1..10}; do
  curl -k -w "%{time_total}\n" -o /dev/null -s https://swinx.test/health
done
```

## 🧹 Cleanup

### Stop Local Production Environment
```bash
# Stop all containers
docker-compose -f docker-compose.local-prod.yml down

# Remove volumes (optional - will delete data)
docker-compose -f docker-compose.local-prod.yml down -v

# Remove images (optional)
docker-compose -f docker-compose.local-prod.yml down --rmi all
```

### Return to Development Environment
```bash
# Start development environment
docker-compose up -d

# Verify development environment
curl http://localhost:8080/health
```

## 🎯 Success Criteria

Your local production environment is working correctly if:

1. ✅ **HTTPS Access**: https://swinx.test loads without errors (ignoring self-signed certificate warning)
2. ✅ **HTTP Redirect**: http://swinx.test redirects to HTTPS
3. ✅ **Security Headers**: All production security headers are present
4. ✅ **Performance**: Response times are fast (<1000ms)
5. ✅ **Compression**: Gzip/Brotli compression is working
6. ✅ **Caching**: Static files have proper cache headers
7. ✅ **Database**: Production database connection works
8. ✅ **Redis**: Production Redis caching works
9. ✅ **Rate Limiting**: Sensitive endpoints have rate limiting
10. ✅ **Logging**: Production logs are being generated

## 📚 Next Steps

After successful local production testing:

1. **Deploy to Staging**: Use the same configuration on a staging server
2. **Real Domain Testing**: Test with a real domain and Let's Encrypt
3. **Performance Monitoring**: Set up monitoring and alerting
4. **Load Testing**: Conduct more comprehensive load testing
5. **Security Audit**: Perform security testing and vulnerability assessment

## 🔗 Related Documentation

- [FrankenPHP Migration Guide](FRANKENPHP_MIGRATION_GUIDE.md)
- [Docker Development Guide](DOCKER_DEVELOPMENT_GUIDE.md)
- [Production Deployment Guide](DEPLOYMENT.md)
