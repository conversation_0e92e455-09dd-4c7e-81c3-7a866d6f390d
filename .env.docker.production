# ===========================================
# Docker Production Environment Configuration
# ===========================================
# For production deployment with docker-compose

# Application (Production)
APP_NAME="Swinx Application"
APP_ENV=production
APP_KEY=base64:i9hHsitTCIzN0rBbPcSxY+M3PLBuBcP1lKzSz51vpfc=
APP_DEBUG=false
APP_TIMEZONE=Asia/Ho_Chi_Minh
APP_URL=https://swinx.test

# Database Configuration (Production)
DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=swinburne
DB_USERNAME=swinx_prod_user
DB_PASSWORD=SwinxProd2024!@#SecurePass
DB_ROOT_PASSWORD=RootProd2024!@#SecurePass

# Redis Configuration (Production)
REDIS_HOST=redis
REDIS_PASSWORD=RedisProd2024!@#SecurePass
REDIS_PORT=6379

# Cache & Session (Production)
CACHE_DRIVER=redis
CACHE_PREFIX=swinx_cache_prod
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=127.0.0.1

# Queue (Production)
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database-uuids

# Logging (Production)
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Broadcasting (Production)
BROADCAST_CONNECTION=redis
PUSHER_APP_ID=your-pusher-app-id
PUSHER_APP_KEY=your-pusher-key
PUSHER_APP_SECRET=your-pusher-secret
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Mail (Production)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-smtp-username
MAIL_PASSWORD=your-smtp-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Filesystem (Production)
FILESYSTEM_DISK=local

# Production Security
SANCTUM_STATEFUL_DOMAINS=swinx.test,www.swinx.test
SESSION_SECURE_COOKIE=true

# Production Tools (Disabled)
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false

# Laravel Package Discovery (Disable dev packages in production)
LARAVEL_IGNORE_PACKAGES="barryvdh/laravel-debugbar"

# SSL/TLS Configuration
FORCE_HTTPS=true

# Performance Optimization
OPCACHE_ENABLE=1
OPCACHE_MEMORY_CONSUMPTION=256
OPCACHE_MAX_ACCELERATED_FILES=20000

# FrankenPHP Configuration (Production)
FRANKENPHP_NUM_THREADS=auto
SERVER_NAME=swinx.test
ACME_EMAIL=<EMAIL>

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30

# Monitoring
SENTRY_LARAVEL_DSN=your-sentry-dsn
HEALTH_CHECK_ENABLED=true

# ===========================================
# IMPORTANT PRODUCTION SECURITY NOTES
# ===========================================
# 1. Change all default passwords and keys
# 2. Use strong, unique passwords for all services
# 3. Enable SSL/TLS certificates
# 4. Configure proper firewall rules
# 5. Set up regular backups
# 6. Monitor application logs
# 7. Keep dependencies updated
# 8. Use environment-specific secrets management

GOOGLE_APPLICATION_CREDENTIALS=storage/xenon-point-407303-ecde80c848d2.json
GOOGLE_DRIVE_FOLDER_ID_EVENT=1cL3ZsWWzVYKMnkoE_52SnPdChIE-IFQY
GOOGLE_DRIVE_FOLDER_ID_QUERY=1EPmGKESv9vpQ_159p4etAoLJN-2wpaYB
GOOGLE_DRIVE_FOLDER_ID_CLUB=1mAzGPeJiph-rep3mHGJSw58IjAW0NR86
GOOGLE_DRIVE_FOLDER_ID_GUIDLINE=10x3esoVT6-SmsN8NVGbwga4Y-TVlb2Oj
GOOGLE_DRIVE_FOLDER_ID_QRCODE=1X64y9uMUfKMBeKopQSlJGiFxxX9gcUZM
GOOGLE_REDIRECT_URI=https://swinx.test/auth/google/callback