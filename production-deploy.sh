#!/bin/bash

# Swinx Production Deployment Script
# Usage: ./production-deploy.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.production.yml"
PROJECT_NAME="swinx"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Swinx Production Deployment${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Deploy application
deploy() {
    print_header
    print_info "Starting production deployment..."
    
    check_docker
    
    # Stop existing containers
    print_info "Stopping existing containers..."
    docker-compose -f $COMPOSE_FILE down || true
    
    # Build and start containers
    print_info "Building and starting containers..."
    docker-compose -f $COMPOSE_FILE up -d --build
    
    # Wait for containers to be healthy
    print_info "Waiting for containers to be healthy..."
    sleep 30
    
    # Check container status
    print_info "Checking container status..."
    docker-compose -f $COMPOSE_FILE ps
    
    # Test health endpoint
    print_info "Testing health endpoint..."
    if curl -f http://localhost:8080/up > /dev/null 2>&1; then
        print_success "Health check passed!"
    else
        print_warning "Health check failed, but containers may still be starting..."
    fi
    
    print_success "Deployment completed!"
    print_info "Application is available at: http://localhost:8080"
}

# Stop application
stop() {
    print_header
    print_info "Stopping production application..."
    
    check_docker
    docker-compose -f $COMPOSE_FILE down
    
    print_success "Application stopped!"
}

# Show status
status() {
    print_header
    print_info "Production application status:"
    
    check_docker
    docker-compose -f $COMPOSE_FILE ps
    
    # Test connectivity
    echo ""
    print_info "Testing connectivity..."
    
    if curl -f http://localhost:8080/up > /dev/null 2>&1; then
        print_success "Application is responding"
    else
        print_error "Application is not responding"
    fi
}

# Show logs
logs() {
    print_header
    print_info "Showing application logs..."
    
    check_docker
    docker-compose -f $COMPOSE_FILE logs -f --tail=50
}

# Restart application
restart() {
    print_header
    print_info "Restarting production application..."
    
    stop
    sleep 5
    deploy
}

# Clean up (remove containers and volumes)
cleanup() {
    print_header
    print_warning "This will remove all containers, networks, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        check_docker
        docker-compose -f $COMPOSE_FILE down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_info "Cleanup cancelled."
    fi
}

# Show help
help() {
    print_header
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  deploy    - Deploy the application (default)"
    echo "  stop      - Stop the application"
    echo "  restart   - Restart the application"
    echo "  status    - Show application status"
    echo "  logs      - Show application logs"
    echo "  cleanup   - Remove all containers and volumes"
    echo "  help      - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 status"
    echo "  $0 logs"
}

# Main script logic
case "${1:-deploy}" in
    deploy)
        deploy
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        help
        exit 1
        ;;
esac
