version: '3.8'

services:
  # Laravel Application with FrankenPHP (Production)
  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: swinx-app
    ports:
      - '80:80'      # HTTP
      - '443:443'    # HTTPS
      - '443:443/udp' # HTTP/3
    env_file:
      - .env.docker.production
    environment:
      - DB_HOST=db
      - REDIS_HOST=redis
      - APP_ENV=production
      - FRANKENPHP_NUM_THREADS=auto
      - SERVER_NAME=${SERVER_NAME:-localhost}
      - ACME_EMAIL=${ACME_EMAIL:-}
    volumes:
      - ./storage:/app/storage
      - ./bootstrap/cache:/app/bootstrap/cache
      # FrankenPHP/Caddy volumes for certificates and configuration
      - caddy_data:/data
      - caddy_config:/config
      # Log directory for production
      - ./logs:/var/log/caddy
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - swinx-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/up"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL Database (same as dev but with production credentials)
  db:
    image: mysql:8.0
    container_name: swinx-db
    ports:
      - '3306:3306'
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-RootProd2024!@#SecurePass}
      MYSQL_DATABASE: ${DB_DATABASE:-swinburne}
      MYSQL_USER: ${DB_USERNAME:-swinx_prod_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-SwinxProd2024!@#SecurePass}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./backups:/backups
    networks:
      - swinx-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

  # Redis Cache (same as dev but with password)
  redis:
    image: redis:7.4-alpine
    container_name: swinx-redis
    ports:
      - '6379:6379'
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-RedisProd2024!@#SecurePass}
    volumes:
      - redis_data:/data
    networks:
      - swinx-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      timeout: 5s
      retries: 5
      interval: 10s

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  # FrankenPHP/Caddy volumes for certificates and configuration
  caddy_data:
    driver: local
  caddy_config:
    driver: local

networks:
  swinx-network:
    driver: bridge
