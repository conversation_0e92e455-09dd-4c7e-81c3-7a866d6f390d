#!/bin/bash

# ===========================================
# Local Production Testing Script
# ===========================================
# Tests FrankenPHP production configuration locally using swinx.test

set -e

echo "🧟 Testing FrankenPHP Local Production Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Test 1: Check hosts file configuration
print_status "Checking hosts file configuration..."
if grep -q "127.0.0.1.*swinx.test" /etc/hosts; then
    print_success "swinx.test is configured in hosts file"
else
    print_error "swinx.test not found in hosts file"
    echo "Please add this line to /etc/hosts:"
    echo "127.0.0.1    swinx.test"
    exit 1
fi

# Test 2: Check if containers are running
print_status "Checking if local production containers are running..."
if docker-compose -f docker-compose.local-prod.yml ps | grep -q "swinx-app-local-prod.*Up"; then
    print_success "Local production application container is running"
else
    print_error "Local production application container is not running"
    echo "Run: docker-compose -f docker-compose.local-prod.yml up -d"
    exit 1
fi

# Test 3: Check FrankenPHP process
print_status "Checking FrankenPHP process..."
if docker exec swinx-app-local-prod pgrep -f frankenphp > /dev/null; then
    print_success "FrankenPHP process is running"
else
    print_error "FrankenPHP process not found"
    echo "Check container logs: docker logs swinx-app-local-prod"
    exit 1
fi

# Test 4: Test HTTP to HTTPS redirect
print_status "Testing HTTP to HTTPS redirect..."
redirect_response=$(curl -s -o /dev/null -w "%{http_code}" -L http://swinx.test/health)
if [ "$redirect_response" = "200" ]; then
    print_success "HTTP to HTTPS redirect works"
else
    print_error "HTTP to HTTPS redirect failed (HTTP code: $redirect_response)"
fi

# Test 5: Test HTTPS connectivity (with self-signed certificate)
print_status "Testing HTTPS connectivity..."
if curl -k -s -o /dev/null -w "%{http_code}" https://swinx.test/health | grep -q "200"; then
    print_success "HTTPS health check passed"
else
    print_error "HTTPS health check failed"
    echo "Check if application is accessible: curl -k -v https://swinx.test/health"
    exit 1
fi

# Test 6: Test Laravel application over HTTPS
print_status "Testing Laravel application over HTTPS..."
response=$(curl -k -s https://swinx.test/up)
if echo "$response" | grep -q "up\|healthy\|ok"; then
    print_success "Laravel application is responding over HTTPS"
else
    print_error "Laravel application not responding correctly over HTTPS"
    echo "Response: $response"
    exit 1
fi

# Test 7: Check SSL certificate details
print_status "Checking SSL certificate details..."
cert_info=$(echo | openssl s_client -connect swinx.test:443 -servername swinx.test 2>/dev/null | openssl x509 -noout -subject 2>/dev/null)
if echo "$cert_info" | grep -q "swinx.test"; then
    print_success "SSL certificate is valid for swinx.test"
    print_info "Certificate: $cert_info"
else
    print_error "SSL certificate issue"
    echo "Certificate info: $cert_info"
fi

# Test 8: Test security headers
print_status "Testing production security headers..."
headers=$(curl -k -s -I https://swinx.test/health)

# Check for HSTS
if echo "$headers" | grep -qi "strict-transport-security"; then
    print_success "HSTS header present"
else
    print_error "HSTS header missing"
fi

# Check for X-Frame-Options
if echo "$headers" | grep -qi "x-frame-options"; then
    print_success "X-Frame-Options header present"
else
    print_error "X-Frame-Options header missing"
fi

# Check for Content-Security-Policy
if echo "$headers" | grep -qi "content-security-policy"; then
    print_success "Content-Security-Policy header present"
else
    print_error "Content-Security-Policy header missing"
fi

# Check for custom environment header
if echo "$headers" | grep -qi "x-environment.*local-production"; then
    print_success "Local production environment header present"
else
    print_error "Local production environment header missing"
fi

# Test 9: Test compression
print_status "Testing compression..."
if curl -k -s -H "Accept-Encoding: gzip" -I https://swinx.test/ | grep -qi "content-encoding.*gzip"; then
    print_success "Gzip compression is working"
else
    print_error "Gzip compression not working"
fi

# Test 10: Test static file serving with caching
print_status "Testing static file serving and caching..."
static_response=$(curl -k -s -I https://swinx.test/favicon.ico)
if echo "$static_response" | grep -q "200"; then
    print_success "Static file serving works"
    if echo "$static_response" | grep -qi "cache-control"; then
        print_success "Static file caching headers present"
    else
        print_error "Static file caching headers missing"
    fi
else
    print_error "Static file serving failed"
fi

# Test 11: Test rate limiting (login endpoint)
print_status "Testing rate limiting on login endpoint..."
# Make multiple requests to test rate limiting
for i in {1..5}; do
    curl -k -s -o /dev/null https://swinx.test/login
done
rate_limit_response=$(curl -k -s -o /dev/null -w "%{http_code}" https://swinx.test/login)
if [ "$rate_limit_response" = "200" ] || [ "$rate_limit_response" = "429" ]; then
    print_success "Rate limiting is configured (response: $rate_limit_response)"
else
    print_error "Rate limiting test failed (response: $rate_limit_response)"
fi

# Test 12: Test database connectivity in production mode
print_status "Testing database connectivity in production mode..."
if docker exec swinx-app-local-prod php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected';" 2>/dev/null | grep -q "Database connected"; then
    print_success "Production database connection successful"
else
    print_error "Production database connection failed"
fi

# Test 13: Test Redis connectivity in production mode
print_status "Testing Redis connectivity in production mode..."
if docker exec swinx-app-local-prod php artisan tinker --execute="Redis::ping(); echo 'Redis connected';" 2>/dev/null | grep -q "Redis connected"; then
    print_success "Production Redis connection successful"
else
    print_error "Production Redis connection failed"
fi

# Test 14: Performance test
print_status "Running HTTPS performance test..."
start_time=$(date +%s%N)
curl -k -s https://swinx.test/health > /dev/null
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))

if [ $duration -lt 1000 ]; then
    print_success "HTTPS response time: ${duration}ms (excellent)"
elif [ $duration -lt 2000 ]; then
    print_success "HTTPS response time: ${duration}ms (good)"
else
    print_error "HTTPS response time: ${duration}ms (slow)"
fi

echo ""
echo "🎉 Local Production Testing Completed!"
echo ""
echo "📊 Summary:"
echo "- Application URL: https://swinx.test"
echo "- HTTP Redirect: http://swinx.test → https://swinx.test"
echo "- Health Check: https://swinx.test/health"
echo "- Laravel Status: https://swinx.test/up"
echo ""
echo "🔧 Useful commands:"
echo "- View logs: docker logs -f swinx-app-local-prod"
echo "- Access container: docker exec -it swinx-app-local-prod bash"
echo "- Restart services: docker-compose -f docker-compose.local-prod.yml restart"
echo "- View certificates: docker exec swinx-app-local-prod frankenphp list-certificates"
echo ""
print_success "All tests passed! Local production FrankenPHP is working correctly."
