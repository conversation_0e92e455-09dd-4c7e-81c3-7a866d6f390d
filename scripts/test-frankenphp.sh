#!/bin/bash

# ===========================================
# FrankenPHP Setup Validation Script
# ===========================================
# Tests the FrankenPHP configuration and setup

set -e

echo "🧟 Testing FrankenPHP Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test 1: Check if containers are running
print_status "Checking if containers are running..."
if docker-compose ps | grep -q "swinx-app.*Up"; then
    print_success "Application container is running"
else
    print_error "Application container is not running"
    echo "Run: docker-compose up -d"
    exit 1
fi

# Test 2: Check FrankenPHP process
print_status "Checking FrankenPHP process..."
if docker exec swinx-app pgrep -f frankenphp > /dev/null; then
    print_success "FrankenPHP process is running"
else
    print_error "FrankenPHP process not found"
    echo "Check container logs: docker logs swinx-app"
    exit 1
fi

# Test 3: Test HTTP connectivity
print_status "Testing HTTP connectivity..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health | grep -q "200"; then
    print_success "HTTP health check passed"
else
    print_error "HTTP health check failed"
    echo "Check if application is accessible: curl -v http://localhost:8080/health"
    exit 1
fi

# Test 4: Test Laravel application
print_status "Testing Laravel application..."
response=$(curl -s http://localhost:8080/up)
if echo "$response" | grep -q "up\|healthy\|ok"; then
    print_success "Laravel application is responding"
else
    print_error "Laravel application not responding correctly"
    echo "Response: $response"
    exit 1
fi

# Test 5: Check database connectivity
print_status "Testing database connectivity..."
if docker exec swinx-app php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected';" 2>/dev/null | grep -q "Database connected"; then
    print_success "Database connection successful"
else
    print_error "Database connection failed"
    echo "Check database container: docker-compose ps"
    exit 1
fi

# Test 6: Check static file serving
print_status "Testing static file serving..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/favicon.ico | grep -q "200"; then
    print_success "Static file serving works"
else
    print_error "Static file serving failed"
    echo "Check if static files are accessible"
fi

# Test 7: Check PHP extensions
print_status "Checking required PHP extensions..."
required_extensions=("pdo_mysql" "mysqli" "zip" "gd" "intl" "mbstring" "opcache" "bcmath" "redis")
for ext in "${required_extensions[@]}"; do
    if docker exec swinx-app php -m | grep -q "$ext"; then
        print_success "PHP extension '$ext' is loaded"
    else
        print_error "PHP extension '$ext' is missing"
    fi
done

# Test 8: Check Caddy configuration
print_status "Validating Caddy configuration..."
if docker exec swinx-app frankenphp validate --config /etc/caddy/Caddyfile 2>/dev/null; then
    print_success "Caddy configuration is valid"
else
    print_error "Caddy configuration validation failed"
    echo "Check Caddyfile syntax"
fi

# Test 9: Check Laravel optimization
print_status "Checking Laravel optimization..."
if docker exec swinx-app php artisan config:show app.env | grep -q "local\|development"; then
    print_success "Laravel is in development mode"
else
    print_error "Laravel environment configuration issue"
fi

# Test 10: Performance test
print_status "Running basic performance test..."
start_time=$(date +%s%N)
curl -s http://localhost:8080/health > /dev/null
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))

if [ $duration -lt 1000 ]; then
    print_success "Response time: ${duration}ms (excellent)"
elif [ $duration -lt 2000 ]; then
    print_success "Response time: ${duration}ms (good)"
else
    print_error "Response time: ${duration}ms (slow)"
fi

echo ""
echo "🎉 FrankenPHP setup validation completed!"
echo ""
echo "📊 Summary:"
echo "- Application URL: http://localhost:8080"
echo "- Health Check: http://localhost:8080/health"
echo "- Laravel Status: http://localhost:8080/up"
echo "- phpMyAdmin: http://localhost:8081"
echo ""
echo "🔧 Useful commands:"
echo "- View logs: docker logs -f swinx-app"
echo "- Access container: docker exec -it swinx-app bash"
echo "- Restart services: docker-compose restart"
echo ""
print_success "All tests passed! FrankenPHP is working correctly."
