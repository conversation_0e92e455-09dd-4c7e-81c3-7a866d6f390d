#!/bin/bash

# ===========================================
# Local Production Setup Script
# ===========================================
# Automates the setup of FrankenPHP production environment locally

set -e

echo "🧟 Setting up FrankenPHP Local Production Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Step 1: Check prerequisites
print_status "Checking prerequisites..."

# Check if hosts file is configured
if grep -q "127.0.0.1.*swinx.test" /etc/hosts; then
    print_success "swinx.test is configured in hosts file"
else
    print_error "swinx.test not found in hosts file"
    echo ""
    echo "Please add this line to your /etc/hosts file:"
    echo "127.0.0.1    swinx.test"
    echo ""
    echo "On macOS/Linux:"
    echo "sudo echo '127.0.0.1    swinx.test' >> /etc/hosts"
    echo ""
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_success "Prerequisites check passed"

# Step 2: Stop any running development environment
print_status "Stopping development environment (if running)..."
docker-compose down > /dev/null 2>&1 || true
print_success "Development environment stopped"

# Step 3: Check for port conflicts
print_status "Checking for port conflicts..."
if lsof -i :80 > /dev/null 2>&1; then
    print_error "Port 80 is in use. Please stop the service using port 80."
    echo "Check what's using port 80: sudo lsof -i :80"
    exit 1
fi

if lsof -i :443 > /dev/null 2>&1; then
    print_error "Port 443 is in use. Please stop the service using port 443."
    echo "Check what's using port 443: sudo lsof -i :443"
    exit 1
fi

print_success "Ports 80 and 443 are available"

# Step 4: Build production images
print_status "Building production Docker images..."
print_info "This may take a few minutes..."
docker-compose -f docker-compose.local-prod.yml build --no-cache

print_success "Production images built successfully"

# Step 5: Start local production environment
print_status "Starting local production environment..."
docker-compose -f docker-compose.local-prod.yml up -d

print_success "Local production environment started"

# Step 6: Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check if containers are running
if docker-compose -f docker-compose.local-prod.yml ps | grep -q "swinx-app-local-prod.*Up"; then
    print_success "Application container is running"
else
    print_error "Application container failed to start"
    echo "Check logs: docker logs swinx-app-local-prod"
    exit 1
fi

# Step 7: Wait for SSL certificate generation
print_status "Waiting for SSL certificate generation..."
print_info "This may take 30-60 seconds..."

# Wait up to 2 minutes for the application to be ready
max_attempts=24
attempt=0
while [ $attempt -lt $max_attempts ]; do
    if curl -k -s -o /dev/null -w "%{http_code}" https://swinx.test/health | grep -q "200"; then
        break
    fi
    attempt=$((attempt + 1))
    echo -n "."
    sleep 5
done

echo ""

if [ $attempt -ge $max_attempts ]; then
    print_error "Application failed to start within 2 minutes"
    echo "Check logs: docker logs swinx-app-local-prod"
    exit 1
fi

print_success "Application is ready and responding"

# Step 8: Test basic functionality
print_status "Running basic functionality tests..."

# Test HTTP redirect
if curl -s -o /dev/null -w "%{http_code}" -L http://swinx.test/health | grep -q "200"; then
    print_success "HTTP to HTTPS redirect works"
else
    print_error "HTTP to HTTPS redirect failed"
fi

# Test HTTPS
if curl -k -s -o /dev/null -w "%{http_code}" https://swinx.test/health | grep -q "200"; then
    print_success "HTTPS connectivity works"
else
    print_error "HTTPS connectivity failed"
fi

# Test Laravel application
if curl -k -s https://swinx.test/up | grep -q "up"; then
    print_success "Laravel application is responding"
else
    print_error "Laravel application is not responding correctly"
fi

# Step 9: Display summary
echo ""
echo "🎉 Local Production Environment Setup Complete!"
echo ""
echo "📊 Environment Details:"
echo "- Application URL: https://swinx.test"
echo "- HTTP Redirect: http://swinx.test → https://swinx.test"
echo "- Environment: Production (local)"
echo "- SSL: Self-signed certificates"
echo ""
echo "🔧 Container Status:"
docker-compose -f docker-compose.local-prod.yml ps
echo ""
echo "🧪 Next Steps:"
echo "1. Open https://swinx.test in your browser (accept certificate warning)"
echo "2. Run comprehensive tests: ./scripts/test-local-production.sh"
echo "3. Check logs: docker logs -f swinx-app-local-prod"
echo ""
echo "📚 Documentation:"
echo "- Local Production Testing Guide: LOCAL_PRODUCTION_TESTING_GUIDE.md"
echo "- FrankenPHP Migration Guide: FRANKENPHP_MIGRATION_GUIDE.md"
echo ""
echo "🛑 To stop the environment:"
echo "docker-compose -f docker-compose.local-prod.yml down"
echo ""
print_success "Setup completed successfully!"
