; PHP Production Configuration for Swinx Application

; Basic Settings
memory_limit = 512M
max_execution_time = 60
max_input_time = 60
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 20

; Error Reporting (Production)
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/www/html/storage/logs/php_errors.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Session Configuration
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Lax"
session.use_strict_mode = 1

; Security
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Performance
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; OPcache (handled in Dockerfile)
; opcache settings are configured in the Dockerfile

; Date/Time
date.timezone = "Asia/Ho_Chi_Minh"

; File Uploads
file_uploads = On
upload_tmp_dir = /tmp

; Logging
log_errors_max_len = 1024
ignore_repeated_errors = On
ignore_repeated_source = Off

; Resource Limits
max_input_vars = 3000
max_input_nesting_level = 64

; Data Handling
default_charset = "UTF-8"

; Connection Handling
default_socket_timeout = 60

; MySQL
mysql.default_socket = /var/run/mysqld/mysqld.sock
mysqli.default_socket = /var/run/mysqld/mysqld.sock

; Paths and Directories
include_path = ".:/usr/local/lib/php"
extension_dir = "/usr/local/lib/php/extensions/no-debug-non-zts-20230831"

; Module Settings
[curl]
curl.cainfo = /etc/ssl/certs/ca-certificates.crt

[openssl]
openssl.cafile = /etc/ssl/certs/ca-certificates.crt
openssl.capath = /etc/ssl/certs
