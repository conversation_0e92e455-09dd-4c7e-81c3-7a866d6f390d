; PHP-FPM Production Configuration for Swinx Application

[www]
user = www
group = www

listen = 127.0.0.1:9000
listen.owner = www
listen.group = www
listen.mode = 0660

; Process Management
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 1000

; Process Naming
process.priority = -19

; Logging
access.log = /var/www/html/storage/logs/fpm-access.log
access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

slowlog = /var/www/html/storage/logs/fpm-slow.log
request_slowlog_timeout = 10s

; Security
security.limit_extensions = .php

; Environment Variables
clear_env = no

; PHP Admin Values
php_admin_value[error_log] = /var/www/html/storage/logs/fpm-php.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 512M
php_admin_value[max_execution_time] = 60

; PHP Values
php_value[session.save_handler] = redis
php_value[session.save_path] = "tcp://redis:6379"
php_value[soap.wsdl_cache_dir] = /tmp

; Health Check
ping.path = /ping
ping.response = pong

; Status Page
pm.status_path = /status

; Catch Workers Output
catch_workers_output = yes
decorate_workers_output = no

; Timeouts
request_terminate_timeout = 120s

; Chroot and Security
;chroot = /var/www/html
;chdir = /

; Resource Limits
rlimit_files = 1024
rlimit_core = 0
