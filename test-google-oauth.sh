#!/bin/bash

# Test Google OAuth Configuration
# Usage: ./test-google-oauth.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Google OAuth Configuration Test${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if container is running
check_container() {
    if ! docker ps | grep -q swinx-app; then
        print_error "Container swinx-app is not running. Please start the application first."
        exit 1
    fi
}

print_header

print_info "Checking Google OAuth configuration..."

check_container

# Get current configuration
print_info "Current configuration:"
echo ""

APP_URL=$(docker exec swinx-app php artisan tinker --execute="echo config('app.url');")
GOOGLE_REDIRECT=$(docker exec swinx-app php artisan tinker --execute="echo config('services.google.redirect');")
GOOGLE_CLIENT_ID=$(docker exec swinx-app php artisan tinker --execute="echo config('services.google.client_id');")

echo "APP_URL: $APP_URL"
echo "GOOGLE_REDIRECT_URI: $GOOGLE_REDIRECT"
echo "GOOGLE_CLIENT_ID: $GOOGLE_CLIENT_ID"
echo ""

# Validate configuration
if [[ "$APP_URL" == "http://127.0.0.1:8080" ]]; then
    print_success "APP_URL is correctly set"
else
    print_error "APP_URL should be 'http://127.0.0.1:8080', got '$APP_URL'"
fi

if [[ "$GOOGLE_REDIRECT" == "http://127.0.0.1:8080/auth/google/callback" ]]; then
    print_success "Google redirect URI is correctly set"
else
    print_error "Google redirect URI should be 'http://127.0.0.1:8080/auth/google/callback', got '$GOOGLE_REDIRECT'"
fi

if [[ -n "$GOOGLE_CLIENT_ID" && "$GOOGLE_CLIENT_ID" != "" ]]; then
    print_success "Google Client ID is set"
else
    print_error "Google Client ID is not set"
fi

echo ""
print_info "Testing application endpoints..."

# Test main application
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    print_success "Main application is accessible"
else
    print_error "Main application is not accessible"
fi

# Test health endpoint
if curl -f http://localhost:8080/up > /dev/null 2>&1; then
    print_success "Health endpoint is working"
else
    print_error "Health endpoint is not working"
fi

echo ""
print_info "Google OAuth Setup Instructions:"
echo ""
echo "1. Go to Google Cloud Console: https://console.cloud.google.com/"
echo "2. Navigate to APIs & Services > Credentials"
echo "3. Edit your OAuth 2.0 Client ID"
echo "4. Add these Authorized JavaScript origins:"
echo "   - http://127.0.0.1:8080"
echo "   - http://localhost:8080"
echo "5. Add these Authorized redirect URIs:"
echo "   - http://127.0.0.1:8080/auth/google/callback"
echo "   - http://localhost:8080/auth/google/callback"
echo "6. Save the changes"
echo ""

print_info "Test URLs:"
echo "- Application: http://localhost:8080"
echo "- Google Login: http://localhost:8080/auth/google"
echo ""

print_warning "Make sure to update Google OAuth settings before testing login!"
