# 🧟 Local Production Quick Reference

Quick commands and URLs for testing FrankenPHP production configuration locally.

## 🚀 Quick Setup

```bash
# 1. Automated setup (recommended)
./scripts/setup-local-production.sh

# 2. Manual setup
docker-compose -f docker-compose.local-prod.yml up -d

# 3. Run tests
./scripts/test-local-production.sh
```

## 🌐 URLs

| Service | URL | Description |
|---------|-----|-------------|
| **Application (HTTPS)** | https://swinx.test | Main application |
| **Application (HTTP)** | http://swinx.test | Redirects to HTTPS |
| **Health Check** | https://swinx.test/health | Health endpoint |
| **Laravel Status** | https://swinx.test/up | Laravel status |

## 🔧 Commands

### Container Management
```bash
# Start local production
docker-compose -f docker-compose.local-prod.yml up -d

# Stop local production
docker-compose -f docker-compose.local-prod.yml down

# View logs
docker logs -f swinx-app-local-prod

# Access container
docker exec -it swinx-app-local-prod bash

# Restart services
docker-compose -f docker-compose.local-prod.yml restart
```

### Testing Commands
```bash
# Quick HTTP test
curl -v http://swinx.test/health

# Quick HTTPS test (ignore SSL warning)
curl -k -v https://swinx.test/health

# Test Laravel application
curl -k https://swinx.test/up

# Check security headers
curl -k -I https://swinx.test/health

# Test compression
curl -k -H "Accept-Encoding: gzip" -I https://swinx.test/
```

### SSL Certificate Commands
```bash
# List certificates
docker exec swinx-app-local-prod frankenphp list-certificates

# Reload certificates
docker exec swinx-app-local-prod frankenphp reload

# Check certificate details
echo | openssl s_client -connect swinx.test:443 -servername swinx.test 2>/dev/null | openssl x509 -noout -text
```

### Laravel Commands
```bash
# Access Laravel Tinker
docker exec -it swinx-app-local-prod php artisan tinker

# Check database connection
docker exec swinx-app-local-prod php artisan tinker --execute="DB::connection()->getPdo(); echo 'Connected';"

# Check Redis connection
docker exec swinx-app-local-prod php artisan tinker --execute="Redis::ping(); echo 'Connected';"

# Clear caches
docker exec swinx-app-local-prod php artisan cache:clear
docker exec swinx-app-local-prod php artisan config:clear
docker exec swinx-app-local-prod php artisan route:clear
```

## 🔍 Verification Checklist

### ✅ Basic Tests
```bash
# 1. HTTP redirect test
curl -I http://swinx.test/health | grep -i location

# 2. HTTPS connectivity test
curl -k -o /dev/null -s -w "%{http_code}\n" https://swinx.test/health

# 3. Laravel application test
curl -k -s https://swinx.test/up

# 4. SSL certificate test
echo | openssl s_client -connect swinx.test:443 -servername swinx.test 2>/dev/null | openssl x509 -noout -subject
```

### ✅ Security Headers Test
```bash
# Check all security headers at once
curl -k -I https://swinx.test/health | grep -E "(Strict-Transport-Security|X-Frame-Options|Content-Security-Policy|X-Content-Type-Options)"
```

### ✅ Performance Tests
```bash
# Response time test
curl -k -w "%{time_total}\n" -o /dev/null -s https://swinx.test/health

# Compression test
curl -k -H "Accept-Encoding: gzip" -I https://swinx.test/ | grep -i content-encoding

# Static file caching test
curl -k -I https://swinx.test/favicon.ico | grep -i cache-control
```

## 🐛 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using ports 80/443
sudo lsof -i :80
sudo lsof -i :443

# Stop common conflicting services
sudo systemctl stop nginx
sudo systemctl stop apache2
```

#### SSL Certificate Issues
```bash
# Force certificate regeneration
docker exec swinx-app-local-prod frankenphp reload

# Check Caddy configuration
docker exec swinx-app-local-prod frankenphp validate --config /etc/caddy/Caddyfile
```

#### Application Not Responding
```bash
# Check container status
docker-compose -f docker-compose.local-prod.yml ps

# Check application logs
docker logs swinx-app-local-prod

# Check database connection
docker exec swinx-app-local-prod php artisan migrate:status
```

#### Hosts File Issues
```bash
# Verify hosts file
grep swinx.test /etc/hosts

# Add if missing (macOS/Linux)
echo "127.0.0.1    swinx.test" | sudo tee -a /etc/hosts
```

## 📊 Expected Results

### HTTP Redirect Test
```bash
$ curl -I http://swinx.test/health
HTTP/1.1 301 Moved Permanently
Location: https://swinx.test/health
```

### HTTPS Health Check
```bash
$ curl -k https://swinx.test/health
healthy
```

### Security Headers
```bash
$ curl -k -I https://swinx.test/health
HTTP/2 200
strict-transport-security: max-age=3600; includeSubDomains
x-frame-options: SAMEORIGIN
content-security-policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;
x-environment: local-production
```

### Performance Metrics
- **Response Time**: < 1000ms
- **Compression**: Gzip/Brotli enabled
- **Caching**: Static files cached for 1 hour
- **HTTP/2**: Enabled by default

## 🔄 Environment Switching

### Switch to Development
```bash
# Stop local production
docker-compose -f docker-compose.local-prod.yml down

# Start development
docker-compose up -d

# Verify development
curl http://localhost:8080/health
```

### Switch Back to Local Production
```bash
# Stop development
docker-compose down

# Start local production
./scripts/setup-local-production.sh
```

## 📚 Documentation Links

- [Local Production Testing Guide](LOCAL_PRODUCTION_TESTING_GUIDE.md)
- [FrankenPHP Migration Guide](FRANKENPHP_MIGRATION_GUIDE.md)
- [Docker Development Guide](DOCKER_DEVELOPMENT_GUIDE.md)
