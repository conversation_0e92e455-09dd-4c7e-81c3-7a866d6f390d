# 🚀 Swinx Production Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying and managing the Swinx Laravel application in production using Docker.

## 🏗️ Architecture

- **Application**: Laravel 11 with Inertia.js + Vue.js
- **Database**: MySQL 8.0
- **Cache**: Redis 7.4
- **Web Server**: Nginx
- **Process Manager**: Supervisor
- **Container**: Docker with Alpine Linux

## 🚀 Quick Start

### 1. Deploy Application

```bash
# Deploy the application
./production-deploy.sh deploy

# Check status
./production-deploy.sh status

# View logs
./production-deploy.sh logs
```

### 2. Database Management

```bash
# Run migrations
./production-db.sh migrate

# Run seeders
./production-db.sh seed

# Check migration status
./production-db.sh status

# Create backup
./production-db.sh backup
```

## 📁 Important Files

### Deployment Scripts
- `production-deploy.sh` - Main deployment management
- `production-db.sh` - Database management
- `docker-compose.production.yml` - Production container orchestration

### Docker Configuration
- `Dockerfile.production` - Production image build
- `docker/start-production.sh` - Production startup script
- `docker/nginx-production.conf` - Nginx configuration
- `docker/supervisord-production.conf` - Process management

### Environment
- `.env.docker.production` - Production environment variables

## 🌐 Access Points

- **Main Application**: http://localhost:8080
- **Health Check**: http://localhost:8080/up
- **Database**: localhost:3306
- **Redis**: localhost:6379

## 🛠️ Management Commands

### Application Management

```bash
# Deploy application
./production-deploy.sh deploy

# Stop application
./production-deploy.sh stop

# Restart application
./production-deploy.sh restart

# Check status
./production-deploy.sh status

# View logs
./production-deploy.sh logs

# Clean up (removes all data!)
./production-deploy.sh cleanup
```

### Database Management

```bash
# Run migrations
./production-db.sh migrate

# Run seeders
./production-db.sh seed

# Refresh migrations (DELETES DATA!)
./production-db.sh migrate:refresh

# Fresh install with seeding (DELETES DATA!)
./production-db.sh fresh

# Check migration status
./production-db.sh status

# Rollback last migration
./production-db.sh rollback

# Create backup
./production-db.sh backup

# Restore from backup
./production-db.sh restore backups/backup_file.sql

# Access database console
./production-db.sh console
```

## 🔧 Manual Docker Commands

If you prefer using Docker commands directly:

```bash
# Start services
docker-compose -f docker-compose.production.yml up -d

# Stop services
docker-compose -f docker-compose.production.yml down

# View logs
docker-compose -f docker-compose.production.yml logs -f

# Execute commands in container
docker exec swinx-app php artisan migrate --force
docker exec swinx-app php artisan db:seed --force
docker exec swinx-app php artisan cache:clear
```

## 🔍 Troubleshooting

### Container Issues

```bash
# Check container status
docker-compose -f docker-compose.production.yml ps

# View application logs
docker logs swinx-app

# View database logs
docker logs swinx-db

# View Redis logs
docker logs swinx-redis
```

### Database Issues

```bash
# Check database connection
docker exec swinx-app php artisan tinker
# Then run: DB::connection()->getPdo();

# Access database directly
docker exec -it swinx-db mysql -u root -p swinx
```

### Application Issues

```bash
# Clear all caches
docker exec swinx-app php artisan optimize:clear

# Rebuild caches
docker exec swinx-app php artisan optimize

# Check application status
curl http://localhost:8080/up
```

## 📊 Monitoring

### Health Checks

```bash
# Application health
curl http://localhost:8080/up

# Database health
docker exec swinx-db mysqladmin ping -h localhost

# Redis health
docker exec swinx-redis redis-cli ping
```

### Performance Monitoring

```bash
# Container resource usage
docker stats

# Application logs
docker logs swinx-app --tail 100 -f

# Database slow query log
docker exec swinx-db mysql -u root -p -e "SHOW VARIABLES LIKE 'slow_query_log';"
```

## 🔒 Security Considerations

1. **Environment Variables**: Ensure all sensitive data is in `.env.docker.production`
2. **Database Access**: Change default passwords in production
3. **SSL/TLS**: Configure HTTPS for production domains
4. **Firewall**: Restrict access to necessary ports only
5. **Backups**: Regular automated backups

## 📦 Backup Strategy

### Automated Backups

```bash
# Create backup script
cat > backup-cron.sh << 'EOF'
#!/bin/bash
cd /path/to/swinx
./production-db.sh backup
# Keep only last 7 days of backups
find backups/ -name "*.sql" -mtime +7 -delete
EOF

# Add to crontab (daily at 2 AM)
echo "0 2 * * * /path/to/swinx/backup-cron.sh" | crontab -
```

## 🚨 Emergency Procedures

### Application Recovery

```bash
# Stop all services
./production-deploy.sh stop

# Clean up
./production-deploy.sh cleanup

# Redeploy
./production-deploy.sh deploy

# Restore database
./production-db.sh restore backups/latest_backup.sql
```

### Data Recovery

```bash
# Restore from backup
./production-db.sh restore backups/backup_file.sql

# Or restore specific tables
docker exec -i swinx-db mysql -u root -p swinx < table_backup.sql
```

## 📞 Support

For issues and questions:

1. Check logs: `./production-deploy.sh logs`
2. Check status: `./production-deploy.sh status`
3. Test health: `curl http://localhost:8080/up`
4. Review this documentation

---

**Production deployment completed successfully! 🎉**
