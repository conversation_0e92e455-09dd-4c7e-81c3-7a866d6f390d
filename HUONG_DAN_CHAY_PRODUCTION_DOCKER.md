# 🚀 HƯỚNG DẪN CHẠY DỰ ÁN PRODUCTION BẰNG DOCKER COMPOSE

## 📋 **TỔNG QUAN**
Hướng dẫn chi tiết để triển khai ứng dụng SWINX Laravel trong môi trường production sử dụng Docker Compose với các tính năng bảo mật và hiệu suất cao.

---

## 🎯 **YÊU CẦU HỆ THỐNG**
- Docker Desktop đã cài đặt và đang chạy
- Docker Compose v2.0+
- Git repository đã clone
- Terminal/Command Line
- Ít nhất 4GB RAM và 10GB dung lượng ổ cứng

---

## 🔧 **CHUẨN BỊ TRIỂN KHAI**

### **Bước 1: Kiểm tra môi trường**
```bash
# Kiểm tra Docker
docker --version
docker-compose --version

# Kiểm tra Docker đang chạy
docker ps

# Kiểm tra dung lượng ổ cứng
df -h
```

### **Bước 2: Dọn dẹp môi trường cũ**
```bash
# Dừng tất cả container đang chạy
docker-compose down -v --remove-orphans

# Xóa các image và volume không sử dụng
docker system prune -f
docker volume prune -f

# Kiểm tra trạng thái sạch
docker ps -a
docker images
```

### **Bước 3: Cấu hình môi trường production**
```bash
# Copy file môi trường production
cp .env.docker.production .env

# Tạo thư mục cần thiết
mkdir -p ssl storage/logs/nginx backups logs

# Kiểm tra file cấu hình
cat .env | head -20
```

---

## 🔐 **CẤU HÌNH BẢO MẬT**

### **Tạo SSL Certificate (Self-signed cho test)**
```bash
# Tạo thư mục SSL
mkdir -p ssl

# Tạo private key
openssl genrsa -out ssl/privkey.pem 2048

# Tạo certificate
openssl req -new -x509 -key ssl/privkey.pem -out ssl/fullchain.pem -days 365 \
  -subj "/C=VN/ST=HCM/L=HoChiMinh/O=Swinx/CN=localhost"

# Kiểm tra certificate
openssl x509 -in ssl/fullchain.pem -text -noout | head -10
```

### **Cập nhật mật khẩu bảo mật**
```bash
# Tạo APP_KEY mới
openssl rand -base64 32

# Cập nhật .env với APP_KEY mới
# Thay thế: APP_KEY=base64:YOUR_NEW_KEY_HERE
```

---

## 🚀 **TRIỂN KHAI PRODUCTION**

### **Bước 1: Build và khởi động services**
```bash
# Build và khởi động tất cả services
docker-compose -f docker-compose.production.yml up -d --build

# Theo dõi quá trình khởi động
docker-compose -f docker-compose.production.yml logs -f
```

### **Bước 2: Kiểm tra trạng thái services**
```bash
# Kiểm tra tất cả container
docker-compose -f docker-compose.production.yml ps

# Kết quả mong đợi:
# ✅ swinx-nginx     - Up (healthy)
# ✅ swinx-app       - Up (healthy)
# ✅ swinx-db        - Up (healthy)
# ✅ swinx-redis     - Up (healthy)
# ✅ swinx-queue     - Up
# ✅ swinx-scheduler - Up
```

### **Bước 3: Kiểm tra logs nếu có lỗi**
```bash
# Kiểm tra logs của từng service
docker logs swinx-app --tail 50
docker logs swinx-nginx --tail 20
docker logs swinx-db --tail 20
docker logs swinx-redis --tail 10

# Kiểm tra health check
docker inspect swinx-app | grep -A 10 "Health"
```

---

## 🌐 **ĐIỂM TRUY CẬP**

| Service | URL | Mục đích |
|---------|-----|----------|
| **Ứng dụng chính** | https://localhost | Laravel Application (HTTPS) |
| **HTTP Redirect** | http://localhost | Tự động chuyển hướng đến HTTPS |
| **Health Check** | https://localhost/health | Kiểm tra tình trạng ứng dụng |

---

## 🔍 **KIỂM TRA VÀ XÁC THỰC**

### **Test kết nối cơ bản**
```bash
# Test HTTP (sẽ redirect đến HTTPS)
curl -I http://localhost

# Test HTTPS (bỏ qua SSL verification cho self-signed cert)
curl -k -I https://localhost

# Test health check
curl -k https://localhost/health

# Kết quả mong đợi: HTTP/1.1 200 OK
```

### **Kiểm tra database**
```bash
# Kết nối database
docker exec -it swinx-db mysql -u swinx_prod_user -pSwinxProd2024!@#SecurePass swinburne

# Trong MySQL shell:
# SHOW TABLES;
# SELECT COUNT(*) FROM users;
# EXIT;
```

### **Kiểm tra Redis**
```bash
# Kết nối Redis
docker exec -it swinx-redis redis-cli -a RedisProd2024!@#SecurePass

# Trong Redis shell:
# PING
# INFO
# EXIT
```

---

## 📊 **GIÁM SÁT VÀ BẢO TRÌ**

### **Theo dõi tài nguyên**
```bash
# Kiểm tra sử dụng tài nguyên
docker stats --no-stream

# Kiểm tra dung lượng
docker system df

# Kiểm tra logs
docker-compose -f docker-compose.production.yml logs --tail 100
```

### **Backup database**
```bash
# Tạo backup
docker exec swinx-db mysqldump -u root -pRootProd2024!@#SecurePass \
  --all-databases --routines --triggers > backups/backup_$(date +%Y%m%d_%H%M%S).sql

# Kiểm tra backup
ls -la backups/
```

### **Cập nhật ứng dụng**
```bash
# Pull code mới
git pull origin main

# Rebuild và restart
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d --build

# Chạy migration
docker exec swinx-app php artisan migrate --force

# Clear cache
docker exec swinx-app php artisan optimize:clear
docker exec swinx-app php artisan optimize
```

---

## 🛠️ **XỬ LÝ SỰ CỐ THƯỜNG GẶP**

### **Sự cố 1: Container không khởi động được**
**Triệu chứng:** Container bị restart liên tục
**Giải pháp:**
```bash
# Kiểm tra logs chi tiết
docker logs swinx-app --tail 50

# Kiểm tra file .env
cat .env | grep -E "DB_|APP_|REDIS_"

# Rebuild từ đầu
docker-compose -f docker-compose.production.yml down -v
docker-compose -f docker-compose.production.yml up -d --build
```

### **Sự cố 2: Lỗi SSL Certificate**
**Triệu chứng:** "SSL certificate problem" hoặc "certificate verify failed"
**Giải pháp:**
```bash
# Tạo lại SSL certificate
rm -rf ssl/*
mkdir -p ssl
openssl genrsa -out ssl/privkey.pem 2048
openssl req -new -x509 -key ssl/privkey.pem -out ssl/fullchain.pem -days 365 \
  -subj "/C=VN/ST=HCM/L=HoChiMinh/O=Swinx/CN=localhost"

# Restart nginx
docker-compose -f docker-compose.production.yml restart nginx
```

### **Sự cố 3: Database connection failed**
**Triệu chứng:** "Access denied" hoặc "Connection refused"
**Giải pháp:**
```bash
# Kiểm tra database container
docker logs swinx-db --tail 30

# Reset database
docker-compose -f docker-compose.production.yml stop db
docker volume rm swinx_mysql_data
docker-compose -f docker-compose.production.yml up -d db

# Đợi database khởi động hoàn toàn
sleep 60
docker-compose -f docker-compose.production.yml up -d app
```

---

## 🎯 **CHECKLIST TRIỂN KHAI THÀNH CÔNG**

### **Trước khi triển khai:**
- [ ] Docker Desktop đang chạy
- [ ] Repository đã cập nhật mới nhất
- [ ] File .env.docker.production đã cấu hình
- [ ] SSL certificates đã tạo
- [ ] Ports 80, 443 không bị chiếm dụng
- [ ] Đủ dung lượng ổ cứng (>10GB)

### **Trong quá trình triển khai:**
- [ ] Tất cả container build thành công
- [ ] Không có lỗi trong logs
- [ ] Health checks đều PASS
- [ ] Database migrations chạy thành công
- [ ] SSL certificate hoạt động

### **Sau khi triển khai:**
- [ ] Ứng dụng truy cập được qua HTTPS
- [ ] HTTP tự động redirect đến HTTPS
- [ ] Database kết nối thành công
- [ ] Redis cache hoạt động
- [ ] Queue worker đang chạy
- [ ] Scheduler hoạt động
- [ ] Backup tự động được thiết lập

---

## 📞 **HỖ TRỢ VÀ KHẮC PHỤC SỰ CỐ**

### **Lệnh debug hữu ích:**
```bash
# Kiểm tra tất cả container
docker ps -a

# Kiểm tra logs realtime
docker-compose -f docker-compose.production.yml logs -f

# Vào container để debug
docker exec -it swinx-app bash

# Kiểm tra kết nối database từ app
docker exec swinx-app php artisan tinker
# Trong tinker: DB::connection()->getPdo();

# Kiểm tra cấu hình Laravel
docker exec swinx-app php artisan config:show database
```

### **Liên hệ hỗ trợ:**
1. Kiểm tra hướng dẫn này trước
2. Xem logs container: `docker logs <container_name>`
3. Kiểm tra file cấu hình .env
4. Thử rebuild clean: `docker-compose down -v && docker-compose up -d --build`

---

**Cập nhật lần cuối:** Tháng 12/2024
**Phiên bản Docker:** 24.0+
**Phiên bản Docker Compose:** 2.0+
**Phiên bản Laravel:** 11.x
**Phiên bản PHP:** 8.4
